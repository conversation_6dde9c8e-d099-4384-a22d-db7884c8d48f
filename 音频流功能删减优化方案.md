# SK Terminal 音频流功能删减优化方案

## 1. 删减目标

保留纯音频流功能，删除所有非必要的控制、管理和调试功能，实现最小化的音频传输系统。

## 2. 可以完全删除的功能模块

### 2.1 Protocol模块中可删除的文件
```
main/protocol/sk_clink.c          # 控制链路，非音频流必需
main/protocol/sk_mqtt.c           # MQTT协议，非音频流必需  
main/protocol/sk_websocket.c      # WebSocket协议，非音频流必需
```

### 2.2 sk_frame.c中可删除的函数
```c
// 控制消息编码函数 - 全部删除
void EncodeRegisterReqMsg()           # 注册请求消息
void EncodeStateReportMsg()           # 状态报告消息
void EncodeCallRequestMsg()           # 呼叫请求消息
void EncodeRelayReqMsg()              # 中继请求消息
void EncodeTermOnhookOffhookMsg()     # 摘机挂机消息
void EncodeRelayAlignAudioMsg()       # 音频对齐消息
void EncodeTermToCloudAiMsg()         # 云端AI消息

// 辅助函数 - 全部删除
void EncodeSessionHead()              # 会话头编码
void GenCtrlMsgBasicInfo()            # 控制消息基本信息
```

**保留函数**:
```c
void EncodeFrameHead()                # 帧头编码 - 音频帧必需
void EncodeRelayDataMsg()             # 数据消息编码 - 音频帧必需
```

### 2.3 sk_frame.h中可删除的定义

**删除的帧类型**:
```c
// 控制消息帧类型
FRAME_CMSG_TERM_TO_CTRL = 1,
FRAME_CMSG_CTRL_TO_TERM = 2,
FRAME_CMSG_CTRL_TO_RELAY = 3,
FRAME_CMSG_RELAY_TO_CTRL = 4,
FRAME_CMSG_TERM_TO_RELAY = 5,
FRAME_CMSG_CTRL_TO_AGENT = 7,
FRAME_CMSG_AGENT_TO_CTRL = 8,
FRAME_CMSG_TERM_AND_TERM = 9,
FRAME_CMSG_AGENT_TO_RELAY = 10,
FRAME_CMSG_AGENT_AND_TERM = 11,
FRAME_DPKT_AGENT_AND_RELAY = 12,
FRAME_CMSG_DFX_KEEPALIVE = 24,
FRAME_CMSG_DFX_AND_TERM = 25,
```

**保留的帧类型**:
```c
FRAME_DPKT_TERM_AND_RELAY = 6,        # 音频数据帧 - 必需
```

**删除的消息类型**: 所有MSG_*定义，只保留`MSG_TYPE_INVALID = 0`

**删除的数据结构**:
```c
typedef struct SessionHead            # 会话头
typedef struct CtrlMsg                # 控制消息
typedef struct CtrlMsgCalleeMatch     # 呼叫匹配消息
typedef struct CtrlMsgFrame           # 控制消息帧
typedef struct CtrlFrameCalleeMatch   # 呼叫匹配帧
typedef struct DbgSysConfig           # 调试系统配置
typedef struct DbgParamData           # 调试参数数据
typedef struct DbgFrame               # 调试帧
```

**保留的数据结构**:
```c
typedef struct FrameHead              # 帧头 - 必需
typedef struct DataFrame              # 数据帧 - 必需
```

## 3. sk_rlink.c中可删除的功能

### 3.1 可删除的结构体字段

```c
typedef struct {
    // 可删除的字段
    uint32_t exitReason;              # 退出原因 - 简化错误处理
    uint16_t sessionID;               # 会话ID - 无会话管理
    SkRlinkCodedDataEndCallback codedDataEndCallback; # 结束回调 - 简化
    uint32_t sendSuccBytes;           # 发送成功统计 - 删除统计
    uint32_t sendFailBytes;           # 发送失败统计 - 删除统计  
    uint32_t recvDataCnt;             # 接收计数统计 - 删除统计
    uint32_t rxTimeoutCnt;            # 超时计数统计 - 删除统计
    
    // 保留的核心字段
    uint32_t linkFlag;                # 链路状态 - 必需
    char serverIp[16];                # 服务器IP - 必需
    uint16_t port;                    # 端口 - 必需
    int sock;                         # Socket - 必需
    uint16_t sockRdy;                 # Socket状态 - 必需
    uint8_t taskFlag;                 # 任务标志 - 必需
    TaskHandle_t txTaskHandle;        # 发送任务 - 必需
    TaskHandle_t rxTaskHandle;        # 接收任务 - 必需
    QueueHandle_t msgQueue;           # 消息队列 - 必需
    void *recordQueue;                # 录音队列 - 必需
    uint16_t seqID;                   # 序列号 - 必需
    void *decPrivate;                 # 解码私有数据 - 必需
    SkRlinkCodedDataCallback codedDataCallback; # 数据回调 - 必需
} RlinkCtrlInfo;
```

### 3.2 可删除的函数

**统计和调试函数**:
```c
void SkRlinkShowStat()                # 显示统计信息
void RlinkStopCall()                  # 停止调用（包含统计显示）
```

**电源管理函数**:
```c
void SkRlinkSetPm()                   # 电源管理设置
void SkRlinkSetFunFlag()              # 功能标志设置
```

**会话管理函数**:
```c
void SkRlinkSetCodedDataEndCallback() # 结束回调设置
```

### 3.3 可简化的函数

**RlinkSendAudioFrame()** - 简化时间戳记录:
```c
// 删除详细的时间戳记录，只保留基本的seq和len
timeRecord->seq = seq;
timeRecord->len = dataLen;
// 删除其他时间戳字段的设置
```

**RlinkProcFrame()** - 简化协议解析:
```c
// 只处理FRAME_DPKT_TERM_AND_RELAY类型
// 删除其他帧类型的处理逻辑
```

**RlinkProcLocalMsg()** - 简化事件处理:
```c
// 只处理RLINK_EVENT_TX_DATA事件
// 删除RLINK_EVENT_STOP_CALL和RLINK_EVENT_RX_EXIT处理
```

## 4. 优化建议

### 4.1 连接管理优化

**简化连接函数**:
```c
// 删除详细的统计信息初始化
int RlinkConnect(RlinkCtrlInfo *ctrl) {
    // 只保留基本的socket创建和连接
    // 删除统计字段的初始化
}

// 简化断开函数
void RlinkDisconnect(RlinkCtrlInfo *ctrl) {
    // 只关闭socket，删除日志输出
}
```

### 4.2 错误处理优化

**简化错误处理**:
```c
// 删除复杂的错误计数和恢复机制
// 连接失败直接返回错误，不进行重试
// 删除详细的错误日志输出
```

### 4.3 缓冲区管理优化

**sk_audio_buffer.c保持不变** - 这是音频流的核心组件，建议完全保留。

### 4.4 任务结构优化

**保留双任务架构**:
- RlinkMainTask: 处理音频数据发送
- RlinkRxTask: 处理音频数据接收

**简化任务逻辑**:
- 删除复杂的状态管理
- 删除详细的日志输出
- 简化错误处理

## 5. 删减后的核心接口

### 5.1 保留的核心接口

```c
// 初始化和清理
void SkRlinkInit();                   # 初始化
void RlinkDeinit();                   # 清理

// 连接管理  
int RlinkConnect();                   # 建立连接
void RlinkDisconnect();               # 断开连接

// 音频数据接口
void SkRlinkFeedReordAudio();         # 输入录音数据
void SkRlinkSetCodedDataCallback();   # 设置解码回调

// 任务管理
void SkRlinkStartTasks();             # 启动任务
SkRlinkHandler SkRlinkGetHandler();   # 获取句柄
```

### 5.2 删除的接口

```c
// 统计接口
void SkRlinkShowStat();               # 统计显示

// 电源管理接口  
void SkRlinkSetPm();                  # 电源管理
void SkRlinkSetFunFlag();             # 功能标志

// 会话管理接口
void SkRlinkSetCodedDataEndCallback(); # 结束回调
```

## 6. 预期效果

### 6.1 代码量减少
- sk_rlink.c: 从622行减少到约400行 (减少35%)
- sk_frame.c: 从133行减少到约50行 (减少62%)
- sk_frame.h: 从251行减少到约100行 (减少60%)

### 6.2 内存占用减少
- RlinkCtrlInfo结构体减少约32字节
- 删除不必要的全局变量和常量定义
- 减少协议帧类型定义的内存占用

### 6.3 功能简化
- 专注于音频数据传输
- 删除复杂的会话和状态管理
- 简化错误处理逻辑
- 提高代码可读性和维护性

这个删减方案保留了音频流的核心功能，同时大幅简化了代码结构，非常适合只需要音频传输功能的应用场景。
