/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: my_app.c
 * @description: WebSocket音频处理模块
 * @author: <PERSON>
 * @date: 2025-07-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <stdbool.h>
#include "sk_log.h"
#include "sk_sm.h"
#include "my_app.h"

static const char *TAG = "MyApp";
static bool ws_connected = false;
static void *g_smHandler = NULL;

void MyAppInit(void *smHandler) {
    g_smHandler = smHandler;
    ws_connected = false;
    SK_LOGI(TAG, "MyApp module initialized");
}

void MyAppStartWebSocketConnection(void) {
    if (!ws_connected) {
        ws_connected = true;
        SK_LOGI(TAG, "WebSocket connection started");
    }
}

void MyAppOnWsEvent(void *arg, void *data, uint16_t len) {
    SK_LOGI(TAG, "WebSocket event received: len=%d", len);
}

void MyAppOnWsAudioData(void *arg, void *data, uint16_t len) {
    SK_LOGI(TAG, "WebSocket audio data received: len=%d", len);
}

void MyAppMainCmdProc(int32_t cmd, void *smHandler) {
    SK_LOGI(TAG, "Command received: %d", cmd);
}

void MyAppGetWebSocketStats(uint32_t *success, uint32_t *error) {
    if (success) *success = 0;
    if (error) *error = 0;
}

void MyAppResetWebSocketStats(void) {
    SK_LOGI(TAG, "Stats reset");
}

bool MyAppIsWebSocketConnected(void) {
    return ws_connected;
}

void MyAppSetWebSocketConnected(bool connected) {
    ws_connected = connected;
}
